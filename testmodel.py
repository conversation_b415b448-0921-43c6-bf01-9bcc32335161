from funasr import AutoModel
# paraformer-zh is a multi-functional asr model
# use vad, punc, spk or not as you need


asr_modeldir = "E:\\work\\asr\\sichuan_asr_model\\sichuan_asr_model"
        # model = AutoModel(model=model_dir,vad_model=vad_modeldir, punc_model=punc_modeldir)


model = AutoModel(model=asr_modeldir,
                  disable_update=True,
                  # spk_model="cam++"
                  batch_size_s=300,
                  hotword='魔搭'
                  )

print(model)